# PP-StructureV3 和 PP-DocTranslation 使用指南

## 概述

本文档介绍如何在 Prisma OCR & Translator 项目中使用 PaddleOCR 的最新文档处理技术：

- **PP-StructureV3**：文档结构化分析产线，支持文档解析、表格识别、公式识别、印章识别等功能
- **PP-DocTranslation**：文档翻译产线，基于 PP-StructureV3 提供端到端的文档识别和翻译功能

PP-DocTranslation 是 PaddleOCR 最新推出的文档翻译产线，它内部使用 PP-StructureV3 作为底层的文档解析引擎，提供更强大和完整的文档处理能力。

## 功能特性

### 核心功能
- **版面区域检测**：自动识别文档中的文本、表格、图片、公式等区域
- **文本识别**：高精度的文字识别，支持多语言
- **表格识别**：结构化表格识别，输出HTML格式
- **公式识别**：数学公式识别和LaTeX输出
- **印章识别**：印章文本检测和识别
- **文档预处理**：文档方向校正、图像矫正等

### 高级特性
- **阅读顺序分析**：智能分析文档的阅读顺序
- **多格式输出**：支持JSON、Markdown、HTML、Excel等多种输出格式
- **批量处理**：支持PDF文件和图像批量处理
- **可视化结果**：提供详细的可视化分析结果

## 安装要求

### 基础要求
- Python 3.8+
- PaddlePaddle 3.0.0+
- PaddleOCR 全功能版本

### 安装命令
```bash
# 安装完整功能版本
python -m pip install "paddleocr[all]"
```

## Python脚本方式集成

### 1. PP-DocTranslation 基础使用（推荐）

```python
from paddleocr import PPDocTranslation

# 初始化PP-DocTranslation产线
pipeline = PPDocTranslation(
    # 文档预处理选项
    use_doc_orientation_classify=False,  # 文档方向分类
    use_doc_unwarping=False,            # 文档扭曲矫正
    use_textline_orientation=True,      # 文本行方向分类

    # 功能模块选项
    use_seal_recognition=True,          # 印章识别
    use_table_recognition=True,         # 表格识别
    use_formula_recognition=True,       # 公式识别
    use_chart_recognition=False,        # 图表解析
    use_region_detection=True,          # 版面区域检测

    # 设备选择
    device='cpu'  # 或 'gpu:0'
)

# 处理文档
input_path = "document.pdf"  # 支持PDF和图像
visual_results = pipeline.visual_predict(input_path)

# 处理结果
for result in visual_results:
    layout_parsing_result = result["layout_parsing_result"]

    # 保存为不同格式
    layout_parsing_result.save_to_json("output.json")
    layout_parsing_result.save_to_markdown("output.md")
    layout_parsing_result.save_to_html("tables.html")
    layout_parsing_result.save_to_xlsx("tables.xlsx")
    layout_parsing_result.save_to_img("visualization/")
```

### 2. PP-StructureV3 直接使用

```python
from paddleocr import PPStructureV3

# 初始化PP-StructureV3产线
pipeline = PPStructureV3(
    # 文档预处理选项
    use_doc_orientation_classify=False,  # 文档方向分类
    use_doc_unwarping=False,            # 文档扭曲矫正
    use_textline_orientation=False,     # 文本行方向分类

    # 功能模块选项
    use_seal_recognition=True,          # 印章识别
    use_table_recognition=True,         # 表格识别
    use_formula_recognition=True,       # 公式识别
    use_chart_recognition=False,        # 图表解析
    use_region_detection=True,          # 版面区域检测

    # 设备选择
    device='cpu'  # 或 'gpu:0'
)
```

### 2. 基础使用示例

```python
import numpy as np
from PIL import Image

# 处理单张图像
def process_single_image(image_path):
    """处理单张图像"""
    
    # 执行预测
    results = pipeline.predict(image_path)
    
    # 处理结果
    for result in results:
        # 打印结果到终端
        result.print()
        
        # 保存为不同格式
        result.save_to_json("output.json")
        result.save_to_markdown("output.md")
        result.save_to_html("tables.html")
        result.save_to_xlsx("tables.xlsx")
        result.save_to_img("visualization/")
    
    return results

# 处理PDF文件
def process_pdf_file(pdf_path):
    """处理PDF文件"""
    
    results = pipeline.predict(pdf_path)
    
    # 合并多页Markdown
    markdown_list = [result.markdown for result in results]
    combined_markdown = results[0].concatenate_markdown_pages(markdown_list)
    
    return results, combined_markdown
```

### 3. 高级参数配置

```python
# 创建带有详细参数的产线
pipeline = PPStructureV3(
    # 版面检测参数
    layout_threshold=0.5,
    layout_nms=True,
    layout_unclip_ratio=1.5,
    layout_merge_bboxes_mode="merge",
    
    # 文本检测参数
    text_det_limit_side_len=960,
    text_det_limit_type="max",
    text_det_thresh=0.3,
    text_det_box_thresh=0.6,
    text_det_unclip_ratio=1.5,
    text_rec_score_thresh=0.5,
    
    # 印章检测参数
    seal_det_limit_side_len=736,
    seal_det_thresh=0.2,
    seal_det_box_thresh=0.6,
    seal_rec_score_thresh=0.0,
    
    # 表格识别参数
    use_wired_table_cells_trans_to_html=False,
    use_wireless_table_cells_trans_to_html=False,
    use_table_orientation_classify=True,
    use_ocr_results_with_table_cells=True,
    use_e2e_wired_table_rec_model=False,
    use_e2e_wireless_table_rec_model=True,
    
    # 性能参数
    enable_hpi=False,
    use_tensorrt=False,
    precision="fp32",
    enable_mkldnn=True,
    cpu_threads=8
)
```

### 4. 批量处理示例

```python
def batch_process_images(image_list):
    """批量处理图像"""
    
    # 支持多种输入格式
    inputs = [
        "image1.jpg",           # 文件路径
        "image2.png",           # 文件路径
        numpy_array,            # numpy数组
        pil_image,              # PIL图像
        "document.pdf"          # PDF文件
    ]
    
    # 批量处理
    results = pipeline.predict(inputs)
    
    # 处理每个结果
    for i, result in enumerate(results):
        print(f"处理第 {i+1} 个文件:")
        result.print(format_json=True, indent=2)
        
        # 保存结果
        result.save_to_json(f"result_{i}.json")
        result.save_to_img(f"visualization_{i}/")
    
    return results
```

### 5. 结果处理和分析

```python
def analyze_results(results):
    """分析处理结果"""
    
    for result in results:
        # 获取JSON格式结果
        json_data = result.json
        
        # 获取可视化图像
        visualization_images = result.img
        
        # 获取Markdown结果
        markdown_data = result.markdown
        
        # 分析解析结果
        parsing_results = json_data.get('parsing_res_list', [])
        
        print(f"检测到 {len(parsing_results)} 个版面区域:")
        for block in parsing_results:
            block_type = block.get('block_label', 'unknown')
            block_content = block.get('block_content', '')
            print(f"  - {block_type}: {block_content[:50]}...")
        
        # 分析表格结果
        table_results = json_data.get('table_res_list', [])
        print(f"检测到 {len(table_results)} 个表格")
        
        # 分析公式结果
        formula_results = json_data.get('formula_res_list', [])
        print(f"检测到 {len(formula_results)} 个公式")
        
        # 分析印章结果
        seal_results = json_data.get('seal_res_list', [])
        print(f"检测到 {len(seal_results)} 个印章")
```

## 在 Prisma 项目中的集成

### 1. 更新OCR引擎

在 `core/ocr_engine.py` 中已经集成了PP-DocTranslation和PP-StructureV3支持：

```python
# 设置为PP-DocTranslation模式（推荐）
ocr_engine.set_mode('doc_translation')

# 或设置为PP-StructureV3模式
ocr_engine.set_mode('structure_v3')

# 处理图像
result = ocr_engine.process_image(image_path)
```

### 2. 配置选项

在 `config/settings.py` 中可以添加PP-DocTranslation相关配置：

```python
class OCRSettings:
    def __init__(self):
        # PP-DocTranslation配置
        self.use_doc_translation = True
        self.doc_translation_config = {
            'use_doc_orientation_classify': False,
            'use_doc_unwarping': False,
            'use_textline_orientation': True,
            'use_seal_recognition': False,
            'use_table_recognition': True,
            'use_formula_recognition': False,
            'use_chart_recognition': False,
            'use_region_detection': True,
            'device': 'cpu'
        }
```

### 3. GUI集成

在GUI中可以添加PP-DocTranslation的选项：

```python
# 添加模式选择
mode_options = ['general', 'table', 'structure_v3', 'doc_translation']

# 根据选择的模式初始化OCR引擎
if selected_mode == 'doc_translation':
    ocr_engine.set_mode('doc_translation')
elif selected_mode == 'structure_v3':
    ocr_engine.set_mode('structure_v3')
```

## 性能优化建议

### 1. 硬件优化
- **GPU加速**：使用GPU可显著提升处理速度
- **内存管理**：确保有足够内存处理大型文档
- **并行处理**：利用多核CPU进行并行处理

### 2. 参数调优
- **检测阈值**：根据文档质量调整检测阈值
- **批次大小**：根据内存情况调整批次大小
- **模型选择**：根据精度和速度需求选择合适的模型

### 3. 使用建议
- **预处理**：对低质量图像进行预处理
- **分页处理**：对大型PDF文件进行分页处理
- **结果缓存**：缓存处理结果避免重复计算

## 常见问题解决

### 1. 安装问题
```bash
# 如果遇到依赖问题，尝试重新安装
pip uninstall paddleocr
pip install "paddleocr[all]"
```

### 2. 内存不足
```python
# 减少批次大小
pipeline = PPStructureV3(
    text_rec_batch_size=1,  # 减少批次大小
    formula_recognition_batch_size=1
)
```

### 3. 处理速度慢
```python
# 启用高性能推理
pipeline = PPStructureV3(
    enable_hpi=True,
    use_tensorrt=True,  # 需要GPU支持
    precision="fp16"    # 使用半精度
)
```

## 版本兼容性

- **PaddleOCR 3.x**：完全支持PP-StructureV3
- **PaddleOCR 2.x**：部分功能可能不可用
- **Python 3.8+**：推荐使用Python 3.9-3.11

## 更多资源

- [PaddleOCR官方文档](https://paddleocr.readthedocs.io/)
- [PP-StructureV3详细文档](https://www.paddleocr.ai/main/version3.x/pipeline_usage/PP-StructureV3.html)
- [模型下载地址](https://github.com/PaddlePaddle/PaddleOCR/blob/main/doc/doc_ch/models_list.md)
- [社区支持](https://github.com/PaddlePaddle/PaddleOCR/issues)

#!/usr/bin/env python3
"""
测试统一OCR引擎实现
验证general和table模式都使用PP-DocTranslation
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import AppSettings
from config.models_config import ModelsConfig
from core.ocr_engine import OCREngine, OCRResult

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_ocr_engine_initialization():
    """测试OCR引擎初始化"""
    print("=" * 60)
    print("测试OCR引擎初始化")
    print("=" * 60)
    
    try:
        # 创建设置和模型配置
        settings = AppSettings()
        models_config = ModelsConfig()
        
        print(f"✅ 配置创建成功")
        print(f"   - 使用GPU: {settings.ocr.use_gpu}")
        print(f"   - 检测模型: {settings.ocr.text_detection_model_name}")
        print(f"   - 识别模型: {settings.ocr.text_recognition_model_name}")
        
        # 创建OCR引擎
        ocr_engine = OCREngine(settings.ocr, models_config)
        print(f"✅ OCR引擎创建成功")
        
        # 测试模式配置
        print(f"\n模式配置:")
        for mode, config in ocr_engine.mode_configs.items():
            print(f"   {mode}模式:")
            print(f"     - 表格识别: {config['use_table_recognition']}")
            print(f"     - 检测模型: {config['text_detection_model_name']}")
            print(f"     - 识别模型: {config['text_recognition_model_name']}")
        
        return ocr_engine
        
    except Exception as e:
        print(f"❌ 初始化失败: {e}")
        return None

def test_mode_switching(ocr_engine):
    """测试模式切换"""
    print("\n" + "=" * 60)
    print("测试模式切换")
    print("=" * 60)
    
    try:
        # 测试general模式
        print("切换到general模式...")
        ocr_engine.set_mode('general')
        print(f"✅ 当前模式: {ocr_engine.current_mode}")
        
        # 测试table模式
        print("切换到table模式...")
        ocr_engine.set_mode('table')
        print(f"✅ 当前模式: {ocr_engine.current_mode}")
        
        # 测试structure_v3模式
        print("切换到structure_v3模式...")
        try:
            ocr_engine.set_mode('structure_v3')
            print(f"✅ 当前模式: {ocr_engine.current_mode}")
        except ValueError as e:
            print(f"⚠️  structure_v3模式不可用: {e}")
        
        # 测试无效模式
        print("测试无效模式...")
        try:
            ocr_engine.set_mode('invalid_mode')
            print("❌ 应该抛出异常")
        except ValueError as e:
            print(f"✅ 正确处理无效模式: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模式切换测试失败: {e}")
        return False

def test_initialization_with_modes(ocr_engine):
    """测试不同模式的初始化"""
    print("\n" + "=" * 60)
    print("测试不同模式的初始化")
    print("=" * 60)
    
    modes_to_test = ['general', 'table']
    
    for mode in modes_to_test:
        print(f"\n测试{mode}模式初始化...")
        try:
            # 设置模式
            ocr_engine.set_mode(mode)
            
            # 设置进度回调
            def progress_callback(progress, status):
                print(f"   进度: {progress}% - {status}")
            
            def error_callback(error_msg):
                print(f"   错误: {error_msg}")
            
            ocr_engine.progress_callback = progress_callback
            ocr_engine.error_callback = error_callback
            
            # 初始化
            success = ocr_engine.initialize()
            
            if success:
                print(f"✅ {mode}模式初始化成功")
                print(f"   - PP-DocTranslation可用: {ocr_engine.pp_doc_translation is not None}")
                print(f"   - 初始化状态: {ocr_engine.is_initialized}")
            else:
                print(f"❌ {mode}模式初始化失败")
            
            # 清理
            ocr_engine.cleanup()
            
        except Exception as e:
            print(f"❌ {mode}模式测试失败: {e}")

def test_configuration_validation():
    """测试配置验证"""
    print("\n" + "=" * 60)
    print("测试配置验证")
    print("=" * 60)
    
    try:
        settings = AppSettings()
        
        # 验证OCR设置
        print("OCR设置验证:")
        print(f"   - 使用DocTranslation: {settings.ocr.use_doc_translation}")
        print(f"   - 检测模型: {settings.ocr.text_detection_model_name}")
        print(f"   - 识别模型: {settings.ocr.text_recognition_model_name}")
        
        # 验证DocTranslation配置
        if settings.ocr.doc_translation_config:
            print("DocTranslation配置:")
            for key, value in settings.ocr.doc_translation_config.items():
                print(f"   - {key}: {value}")
        
        print("✅ 配置验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        return False

def main():
    """主测试函数"""
    print("统一OCR引擎测试")
    print("=" * 60)
    
    setup_logging()
    
    # 测试配置验证
    if not test_configuration_validation():
        return False
    
    # 测试OCR引擎初始化
    ocr_engine = test_ocr_engine_initialization()
    if not ocr_engine:
        return False
    
    # 测试模式切换
    if not test_mode_switching(ocr_engine):
        return False
    
    # 测试不同模式的初始化
    test_initialization_with_modes(ocr_engine)
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

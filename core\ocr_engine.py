"""
OCR核心引擎
"""

import os
import logging
import numpy as np
import cv2
from typing import List, Dict, Any, Optional, Callable, Union
from pathlib import Path
from PIL import Image


try:
    import paddleocr
    PADDLEOCR_AVAILABLE = True

    # 尝试导入PP-StructureV3
    try:
        from paddleocr import PPStructureV3
        PP_STRUCTURE_V3_AVAILABLE = True
    except ImportError:
        PP_STRUCTURE_V3_AVAILABLE = False
        PPStructureV3 = None

    # 尝试导入PP-DocTranslation
    try:
        from paddleocr import PPDocTranslation
        PP_DOC_TRANSLATION_AVAILABLE = True
    except ImportError:
        PP_DOC_TRANSLATION_AVAILABLE = False
        PPDocTranslation = None

except ImportError:
    PADDLEOCR_AVAILABLE = False
    PP_STRUCTURE_V3_AVAILABLE = False
    PP_DOC_TRANSLATION_AVAILABLE = False
    PPStructureV3 = None
    PPDocTranslation = None

from config.settings import OCRSettings
from config.models_config import ModelsConfig
from utils.image_utils import ImageUtils


class OCRResult:
    """OCR识别结果"""
    
    def __init__(self):
        self.text_blocks: List[Dict[str, Any]] = []
        self.tables: List[Dict[str, Any]] = []
        self.layout_info: Dict[str, Any] = {}
        self.confidence_scores: List[float] = []
        self.processing_time: float = 0.0
        self.page_number: int = 0
    
    def add_text_block(self, text: str, bbox: List[float], confidence: float):
        """添加文本块"""
        self.text_blocks.append({
            'text': text,
            'bbox': bbox,
            'confidence': confidence
        })
        self.confidence_scores.append(confidence)
    
    def add_table(self, table_data: Dict[str, Any]):
        """添加表格数据"""
        self.tables.append(table_data)
    
    def get_all_text(self) -> str:
        """获取所有识别的文本"""
        return '\n'.join([block['text'] for block in self.text_blocks])
    
    def get_average_confidence(self) -> float:
        """获取平均置信度"""
        if not self.confidence_scores:
            return 0.0
        return sum(self.confidence_scores) / len(self.confidence_scores)


class OCREngine:
    """OCR引擎"""

    def __init__(self, settings: OCRSettings, models_config: ModelsConfig):
        self.settings = settings
        self.models_config = models_config
        self.logger = logging.getLogger(__name__)

        # OCR实例 - 统一使用PP-DocTranslation
        self.pp_doc_translation = None  # PP-DocTranslation实例
        self.pp_structure_v3 = None  # PP-StructureV3实例（保留用于兼容）

        # 初始化状态
        self.is_initialized = False
        self.current_mode = 'general'  # 'general', 'table', 'structure_v3'

        # 回调函数
        self.progress_callback = None
        self.page_processed_callback = None
        self.error_callback = None

        # 模式配置模板
        self.mode_configs = {
            'general': {
                'use_doc_orientation_classify': False,
                'use_doc_unwarping': False,
                'use_textline_orientation': True,
                'use_seal_recognition': False,
                'use_table_recognition': False,  # general模式不启用表格识别
                'use_formula_recognition': False,
                'use_chart_recognition': False,
                'use_region_detection': True,
                'device': 'cpu',
                'text_detection_model_name': "PP-OCRv5_mobile_det",
                'text_recognition_model_name': "PP-OCRv5_mobile_rec"
            },
            'table': {
                'use_doc_orientation_classify': False,
                'use_doc_unwarping': False,
                'use_textline_orientation': True,
                'use_seal_recognition': False,
                'use_table_recognition': True,  # table模式启用表格识别
                'use_formula_recognition': False,
                'use_chart_recognition': False,
                'use_region_detection': True,
                'device': 'cpu',
                'text_detection_model_name': "PP-OCRv5_mobile_det",
                'text_recognition_model_name': "PP-OCRv5_mobile_rec"
            }
        }
    
    def initialize(self) -> bool:
        """初始化OCR引擎"""
        if not PADDLEOCR_AVAILABLE:
            if self.error_callback:
                self.error_callback("PaddleOCR未安装，请安装相关依赖")
            return False

        try:
            if self.progress_callback:
                self.progress_callback(10, "正在初始化OCR引擎...")

            # 初始化PP-DocTranslation（统一使用）
            if PP_DOC_TRANSLATION_AVAILABLE:
                if self.progress_callback:
                    self.progress_callback(50, "正在初始化PP-DocTranslation...")
                try:
                    # 使用当前模式的配置初始化PP-DocTranslation
                    current_config = self.mode_configs[self.current_mode].copy()

                    # 从设置中获取设备配置
                    if hasattr(self.settings, 'use_gpu') and self.settings.use_gpu:
                        current_config['device'] = 'gpu:0'
                    else:
                        current_config['device'] = 'cpu'

                    self.pp_doc_translation = PPDocTranslation(**current_config)
                    self.logger.info(f"PP-DocTranslation初始化成功，模式: {self.current_mode}")
                except Exception as e:
                    self.logger.warning(f"PP-DocTranslation初始化失败: {e}")
                    self.pp_doc_translation = None
                    if self.error_callback:
                        self.error_callback(f"PP-DocTranslation初始化失败: {e}")
                    return False
            else:
                if self.error_callback:
                    self.error_callback("PP-DocTranslation不可用，请安装完整版PaddleOCR")
                return False

            # 初始化PP-StructureV3（保留用于兼容）
            if PP_STRUCTURE_V3_AVAILABLE:
                if self.progress_callback:
                    self.progress_callback(80, "正在初始化PP-StructureV3...")

                try:
                    self.pp_structure_v3 = PPStructureV3(
                        use_doc_orientation_classify=False,
                        use_doc_unwarping=False,
                        use_textline_orientation=False,
                        use_seal_recognition=True,
                        use_table_recognition=True,
                        use_formula_recognition=True,
                        use_chart_recognition=False,
                        use_region_detection=True,
                        text_detection_model_name="PP-OCRv5_mobile_det",
                        text_recognition_model_name="PP-OCRv5_mobile_rec"
                    )
                    self.logger.info("PP-StructureV3初始化成功")
                except Exception as e:
                    self.logger.warning(f"PP-StructureV3初始化失败: {e}")
                    self.pp_structure_v3 = None

            if self.progress_callback:
                self.progress_callback(100, "OCR引擎初始化完成")
            self.is_initialized = True
            return True
            
        except Exception as e:
            error_msg = f"OCR引擎初始化失败: {e}"
            self.logger.error(error_msg)
            if self.error_callback:
                self.error_callback(error_msg)
            return False
    
    def set_mode(self, mode: str):
        """设置处理模式"""
        if mode in ['general', 'table', 'structure_v3']:
            if mode == 'structure_v3' and not PP_STRUCTURE_V3_AVAILABLE:
                raise ValueError("PP-StructureV3不可用，请安装完整版PaddleOCR")
            if mode in ['general', 'table'] and not PP_DOC_TRANSLATION_AVAILABLE:
                raise ValueError("PP-DocTranslation不可用，请安装完整版PaddleOCR")

            # 如果模式发生变化且已初始化，需要重新初始化PP-DocTranslation
            if mode != self.current_mode and self.is_initialized and mode in ['general', 'table']:
                self.current_mode = mode
                self._reinitialize_doc_translation()
            else:
                self.current_mode = mode
        else:
            raise ValueError(f"不支持的模式: {mode}")

    def _reinitialize_doc_translation(self):
        """重新初始化PP-DocTranslation以应用新的模式配置"""
        if not PP_DOC_TRANSLATION_AVAILABLE:
            return

        try:
            # 使用新模式的配置重新初始化
            current_config = self.mode_configs[self.current_mode].copy()

            # 从设置中获取设备配置
            if hasattr(self.settings, 'use_gpu') and self.settings.use_gpu:
                current_config['device'] = 'gpu:0'
            else:
                current_config['device'] = 'cpu'

            self.pp_doc_translation = PPDocTranslation(**current_config)
            self.logger.info(f"PP-DocTranslation重新初始化成功，新模式: {self.current_mode}")
        except Exception as e:
            self.logger.error(f"PP-DocTranslation重新初始化失败: {e}")
            if self.error_callback:
                self.error_callback(f"模式切换失败: {e}")
    
    def process_image(self, image: Union[np.ndarray, Image.Image, str, Path], 
                     page_number: int = 0) -> OCRResult:
        """
        处理单张图像
        
        Args:
            image: 图像数据（numpy数组、PIL图像或文件路径）
            page_number: 页码
            
        Returns:
            OCR识别结果
        """
        if not self.is_initialized:
            raise RuntimeError("OCR引擎未初始化")
        
        result = OCRResult()
        result.page_number = page_number
        
        try:
            import time
            start_time = time.time()
            
            # 预处理图像
            processed_image = self._preprocess_image(image)
            
            if self.current_mode in ['general', 'table']:
                result = self._process_doc_translation_ocr(processed_image, result)
            elif self.current_mode == 'structure_v3':
                result = self._process_structure_v3_ocr(processed_image, result)
            
            result.processing_time = time.time() - start_time
            
            # 调用页面处理完成回调
            if self.page_processed_callback:
                self.page_processed_callback(page_number, result)
            
            return result
            
        except Exception as e:
            error_msg = f"图像处理失败 (页面 {page_number}): {e}"
            self.logger.error(error_msg)
            if self.error_callback:
                self.error_callback(error_msg)
            raise
    
    def _preprocess_image(self, image: Union[np.ndarray, Image.Image, str, Path]) -> np.ndarray:
        """预处理图像"""
        if isinstance(image, (str, Path)):
            # 从文件路径加载
            image = ImageUtils.load_image(image)
        elif isinstance(image, Image.Image):
            # PIL图像转OpenCV
            image = ImageUtils.pil_to_cv2(image)
        elif isinstance(image, np.ndarray):
            # 已经是numpy数组
            pass
        else:
            raise ValueError(f"不支持的图像类型: {type(image)}")

        # 记录原始图像信息
        self.logger.debug(f"预处理图像尺寸: {image.shape}")
        self.logger.debug(f"图像数据类型: {image.dtype}")

        # 图像质量检查
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image

        mean_brightness = np.mean(gray)
        self.logger.debug(f"图像平均亮度: {mean_brightness}")

        # 如果图像过暗或过亮，进行预处理
        if mean_brightness < 50 or mean_brightness > 200:
            self.logger.info(f"图像亮度异常({mean_brightness})，应用增强处理")
            image = self._enhance_image_quality(image)

        return image

    def _enhance_image_quality(self, image: np.ndarray) -> np.ndarray:
        """增强图像质量以提高OCR识别率"""
        try:
            # 转换为灰度图进行分析
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            # 自适应直方图均衡化
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced_gray = clahe.apply(gray)

            # 如果原图是彩色的，保持彩色格式
            if len(image.shape) == 3:
                # 转换到LAB色彩空间进行亮度增强
                lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
                lab[:,:,0] = enhanced_gray
                enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)
            else:
                enhanced = enhanced_gray

            self.logger.debug("应用了图像质量增强")
            return enhanced

        except Exception as e:
            self.logger.warning(f"图像增强失败，使用原图: {e}")
            return image



    def _process_structure_v3_ocr(self, image: Union[np.ndarray, str, Path], result: OCRResult) -> OCRResult:
        """处理PP-StructureV3 OCR"""
        try:
            if not self.pp_structure_v3:
                self.logger.warning("PP-StructureV3未初始化，回退到通用OCR")
                return self._process_general_ocr(image, result)

            self.logger.debug("开始执行PP-StructureV3识别...")

            # 执行PP-StructureV3识别
            structure_results = self.pp_structure_v3.predict(image)

            self.logger.debug(f"PP-StructureV3结果类型: {type(structure_results)}")

            if not structure_results:
                self.logger.warning("PP-StructureV3返回空结果")
                return result

            # 处理结果
            for page_result in structure_results:
                # 处理解析结果列表
                if 'parsing_res_list' in page_result:
                    for block in page_result['parsing_res_list']:
                        block_type = block.get('block_label', 'text')
                        block_content = block.get('block_content', '')
                        block_bbox = block.get('block_bbox', [])

                        if block_type == 'table' and 'table_res_list' in page_result:
                            # 处理表格
                            for table_res in page_result['table_res_list']:
                                table_data = {
                                    'type': 'table',
                                    'bbox': block_bbox,
                                    'html': table_res.get('pred_html', ''),
                                    'confidence': 1.0  # PP-StructureV3不直接提供置信度
                                }
                                result.add_table(table_data)
                        else:
                            # 处理文本块
                            if block_content.strip():
                                result.add_text_block(
                                    text=block_content,
                                    bbox=block_bbox if isinstance(block_bbox, list) else [],
                                    confidence=1.0
                                )

                # 处理全局OCR结果
                if 'overall_ocr_res' in page_result:
                    ocr_res = page_result['overall_ocr_res']
                    if 'rec_texts' in ocr_res and 'rec_polys' in ocr_res:
                        texts = ocr_res['rec_texts']
                        polys = ocr_res['rec_polys']
                        scores = ocr_res.get('rec_scores', [1.0] * len(texts))

                        for text, poly, score in zip(texts, polys, scores):
                            if text.strip():
                                # 将多边形转换为边界框
                                if isinstance(poly, np.ndarray) and poly.shape == (4, 2):
                                    bbox = [
                                        float(poly[:, 0].min()),  # x_min
                                        float(poly[:, 1].min()),  # y_min
                                        float(poly[:, 0].max()),  # x_max
                                        float(poly[:, 1].max())   # y_max
                                    ]
                                else:
                                    bbox = []

                                result.add_text_block(text, bbox, float(score))

            self.logger.info(f"PP-StructureV3成功处理，识别到 {len(result.text_blocks)} 个文本块，{len(result.tables)} 个表格")
            return result

        except Exception as e:
            self.logger.error(f"PP-StructureV3处理失败: {e}")
            # 回退到通用OCR
            return self._process_general_ocr(image, result)

    def _process_doc_translation_ocr(self, image: Union[np.ndarray, str, Path], result: OCRResult) -> OCRResult:
        """处理PP-DocTranslation OCR - 仅使用视觉识别功能，不进行翻译"""
        try:
            if not self.pp_doc_translation:
                self.logger.warning("PP-DocTranslation未初始化，回退到通用OCR")
                return self._process_general_ocr(image, result)

            self.logger.debug("开始执行PP-DocTranslation识别...")

            # 执行PP-DocTranslation视觉预测（不进行翻译，chat_bot_config=None）
            visual_results = self.pp_doc_translation.visual_predict(image)

            self.logger.debug(f"PP-DocTranslation结果类型: {type(visual_results)}")

            if not visual_results:
                self.logger.warning("PP-DocTranslation返回空结果")
                return result

            # 处理结果
            for page_result in visual_results:
                layout_parsing_result = page_result.get("layout_parsing_result")
                if not layout_parsing_result:
                    continue

                # 获取结果数据
                res_data = layout_parsing_result.res if hasattr(layout_parsing_result, 'res') else {}

                # 处理解析结果列表
                parsing_res_list = res_data.get('parsing_res_list', [])
                for block in parsing_res_list:
                    block_type = block.get('block_label', 'text')
                    block_content = block.get('block_content', '')
                    block_bbox = block.get('block_bbox', [])

                    if block_type == 'table':
                        # 处理表格
                        table_data = {
                            'type': 'table',
                            'bbox': block_bbox,
                            'html': block_content,  # PP-DocTranslation直接提供HTML内容
                            'confidence': 1.0
                        }
                        result.add_table(table_data)
                    else:
                        # 处理文本块
                        if block_content.strip():
                            result.add_text_block(
                                text=block_content,
                                bbox=block_bbox if isinstance(block_bbox, list) else [],
                                confidence=1.0
                            )

                # 处理全局OCR结果
                overall_ocr_res = res_data.get('overall_ocr_res', {})
                if 'rec_texts' in overall_ocr_res and 'rec_polys' in overall_ocr_res:
                    texts = overall_ocr_res['rec_texts']
                    polys = overall_ocr_res['rec_polys']
                    scores = overall_ocr_res.get('rec_scores', [1.0] * len(texts))

                    for text, poly, score in zip(texts, polys, scores):
                        if text.strip():
                            # 将多边形转换为边界框
                            if isinstance(poly, np.ndarray) and poly.shape == (4, 2):
                                bbox = [
                                    float(poly[:, 0].min()),  # x_min
                                    float(poly[:, 1].min()),  # y_min
                                    float(poly[:, 0].max()),  # x_max
                                    float(poly[:, 1].max())   # y_max
                                ]
                            else:
                                bbox = []

                            result.add_text_block(text, bbox, float(score))

            self.logger.info(f"PP-DocTranslation成功处理，识别到 {len(result.text_blocks)} 个文本块，{len(result.tables)} 个表格")
            return result

        except Exception as e:
            self.logger.error(f"PP-DocTranslation处理失败: {e}")
            # 回退到通用OCR
            return self._process_general_ocr(image, result)

    def process_multiple_images(self, images: List[Union[np.ndarray, Image.Image, str, Path]],
                              progress_callback: Optional[Callable] = None) -> List[OCRResult]:
        """
        处理多张图像
        
        Args:
            images: 图像列表
            progress_callback: 进度回调函数
            
        Returns:
            OCR结果列表
        """
        results = []
        total_images = len(images)
        
        for i, image in enumerate(images):
            try:
                # 更新进度
                progress = int((i / total_images) * 100)
                status = f"正在处理第 {i+1}/{total_images} 页"
                if self.progress_callback:
                    self.progress_callback(progress, status)
                
                if progress_callback:
                    progress_callback(progress, status)
                
                # 处理图像
                result = self.process_image(image, page_number=i+1)
                results.append(result)
                
            except Exception as e:
                self.logger.error(f"处理第 {i+1} 页失败: {e}")
                # 创建空结果
                empty_result = OCRResult()
                empty_result.page_number = i+1
                results.append(empty_result)
        
        # 完成进度
        if self.progress_callback:
            self.progress_callback(100, f"处理完成，共 {total_images} 页")
        
        return results
    
    def cleanup(self):
        """清理资源"""
        self.pp_structure_v3 = None
        self.pp_doc_translation = None
        self.is_initialized = False

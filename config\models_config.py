"""
模型配置管理
"""

from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class ModelConfig:
    """模型配置"""
    name: str
    url: str
    local_path: str
    size_mb: float
    description: str


class ModelsConfig:
    """模型配置管理器"""
    
    def __init__(self, models_dir: Optional[str] = None):
        self.models_dir = Path(models_dir) if models_dir else Path.cwd() / "resources" / "models"
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
        # OCR模型配置
        self.ocr_models = {
            'det_v4': ModelConfig(
                name='PP-OCRv4检测模型',
                url='https://paddleocr.bj.bcebos.com/PP-OCRv4/chinese/ch_PP-OCRv4_det_infer.tar',
                local_path=str(self.models_dir / 'ch_PP-OCRv4_det_infer'),
                size_mb=4.7,
                description='中文文本检测模型'
            ),
            'rec_v4': ModelConfig(
                name='PP-OCRv4识别模型',
                url='https://paddleocr.bj.bcebos.com/PP-OCRv4/chinese/ch_PP-OCRv4_rec_infer.tar',
                local_path=str(self.models_dir / 'ch_PP-OCRv4_rec_infer'),
                size_mb=10.6,
                description='中文文本识别模型'
            ),
            'det_v5_mobile': ModelConfig(
                name='PP-OCRv5移动端检测模型',
                url='https://paddleocr.bj.bcebos.com/PP-OCRv5/chinese/ch_PP-OCRv5_mobile_det_infer.tar',
                local_path=str(self.models_dir / 'ch_PP-OCRv5_mobile_det_infer'),
                size_mb=3.2,
                description='PP-OCRv5中文移动端文本检测模型'
            ),
            'rec_v5_mobile': ModelConfig(
                name='PP-OCRv5移动端识别模型',
                url='https://paddleocr.bj.bcebos.com/PP-OCRv5/chinese/ch_PP-OCRv5_mobile_rec_infer.tar',
                local_path=str(self.models_dir / 'ch_PP-OCRv5_mobile_rec_infer'),
                size_mb=8.9,
                description='PP-OCRv5中文移动端文本识别模型'
            ),
            'cls': ModelConfig(
                name='文本方向分类模型',
                url='https://paddleocr.bj.bcebos.com/dygraph_v2.0/ch/ch_ppocr_mobile_v2.0_cls_infer.tar',
                local_path=str(self.models_dir / 'ch_ppocr_mobile_v2.0_cls_infer'),
                size_mb=1.38,
                description='文本方向分类模型'
            )
        }
        
        # 表格识别模型配置
        self.table_models = {
            'table_det': ModelConfig(
                name='表格检测模型',
                url='https://paddleocr.bj.bcebos.com/ppstructure/models/slanet/ch_ppstructure_mobile_v2.0_SLANet_infer.tar',
                local_path=str(self.models_dir / 'ch_ppstructure_mobile_v2.0_SLANet_infer'),
                size_mb=9.3,
                description='表格结构识别模型'
            ),
            'layout': ModelConfig(
                name='版面分析模型',
                url='https://paddleocr.bj.bcebos.com/ppstructure/models/layout/picodet_lcnet_x1_0_fgd_layout_infer.tar',
                local_path=str(self.models_dir / 'picodet_lcnet_x1_0_fgd_layout_infer'),
                size_mb=9.7,
                description='版面分析模型'
            )
        }
        
        # 翻译模型配置
        self.translation_models = {
            'doc_translation': ModelConfig(
                name='文档翻译模型',
                url='https://paddlenlp.bj.bcebos.com/models/transformers/doc_translation/doc_translation_zh_en.tar.gz',
                local_path=str(self.models_dir / 'doc_translation_zh_en'),
                size_mb=450.0,
                description='中英文档翻译模型'
            )
        }
    
    def get_all_models(self) -> Dict[str, ModelConfig]:
        """获取所有模型配置"""
        all_models = {}
        all_models.update(self.ocr_models)
        all_models.update(self.table_models)
        all_models.update(self.translation_models)
        return all_models
    
    def is_model_downloaded(self, model_key: str) -> bool:
        """检查模型是否已下载"""
        all_models = self.get_all_models()
        if model_key not in all_models:
            return False
        
        model_path = Path(all_models[model_key].local_path)
        return model_path.exists() and any(model_path.iterdir())
    
    def get_missing_models(self) -> Dict[str, ModelConfig]:
        """获取未下载的模型"""
        all_models = self.get_all_models()
        missing_models = {}
        
        for key, model in all_models.items():
            if not self.is_model_downloaded(key):
                missing_models[key] = model
        
        return missing_models
    
    def get_total_download_size(self) -> float:
        """获取所有模型的总下载大小（MB）"""
        missing_models = self.get_missing_models()
        return sum(model.size_mb for model in missing_models.values())
    
    def get_ocr_model_paths(self) -> Dict[str, str]:
        """获取OCR模型路径"""
        paths = {}
        for key, model in self.ocr_models.items():
            if self.is_model_downloaded(key):
                paths[f'{key}_model_dir'] = model.local_path
        return paths
    
    def get_table_model_paths(self) -> Dict[str, str]:
        """获取表格模型路径"""
        paths = {}
        for key, model in self.table_models.items():
            if self.is_model_downloaded(key):
                if key == 'table_det':
                    paths['table_model_dir'] = model.local_path
                elif key == 'layout':
                    paths['layout_model_dir'] = model.local_path
        return paths
    
    def get_translation_model_paths(self) -> Dict[str, str]:
        """获取翻译模型路径"""
        paths = {}
        for key, model in self.translation_models.items():
            if self.is_model_downloaded(key):
                paths['translation_model_dir'] = model.local_path
        return paths

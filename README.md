# Prisma OCR

本地PDF与图像OCR识别工具 - 基于PaddleOCR构建的桌面应用程序

## 功能特性

### 核心功能
- **通用OCR模式**: 识别并提取文档中的所有文本内容和基础版面结构
- **表格模式**: 专注于识别和提取表格数据，支持结构化输出
- **PP-DocTranslation模式**: 基于最新PP-DocTranslation技术的高精度文档识别
- **多格式输出**: 支持TXT、DOCX、PDF、XLSX等多种输出格式

### 界面特性
- **现代化Web界面**: 基于pywebview的直观用户界面，支持HTML5/CSS3/JavaScript
- **实时进度显示**: 详细的处理进度和状态反馈
- **系统资源监控**: 实时显示CPU、内存、GPU使用情况
- **拖拽支持**: 支持文件拖拽操作
- **响应式设计**: 适配不同屏幕尺寸

### 技术特性
- **本地化运行**: 所有处理在本地完成，保护数据隐私
- **多线程架构**: 确保界面响应性，避免卡顿
- **跨平台支持**: 支持Windows、macOS、Linux
- **GPU加速**: 支持NVIDIA GPU加速处理

## 系统要求

### 基础要求
- Python 3.8+
- 4GB+ RAM
- 2GB+ 可用磁盘空间（用于模型文件）

### 推荐配置
- Python 3.9+
- 8GB+ RAM
- NVIDIA GPU（支持CUDA）
- SSD存储

## 安装说明

### 1. 克隆项目
```bash
git clone <repository-url>
cd Prisma-OCR
```

### 2. 创建虚拟环境（推荐）
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# macOS/Linux
source venv/bin/activate
```

### 3. 安装依赖
```bash
pip install -r requirements.txt
```

### 4. 运行应用程序
```bash
# WebView版本（推荐）
python run_webview.py

# 或者直接运行
python main.py

# 调试模式
python main.py --debug

# 功能测试
python test_webview.py
```

## 使用说明

### 基本使用流程
1. **选择文件**: 点击"浏览"按钮或拖拽文件到输入区域
2. **选择模式**: 根据文档类型选择"通用模式"或"表格模式"
3. **配置选项**: 设置输出格式、翻译选项等
4. **开始处理**: 点击"开始处理"按钮
5. **查看结果**: 处理完成后在指定目录查看输出文件

### 支持的文件格式

#### 输入格式
- PDF文件 (`.pdf`)
- 图像文件 (`.png`, `.jpg`, `.jpeg`, `.bmp`, `.tiff`, `.webp`, `.gif`)

#### 输出格式
- 纯文本 (`.txt`)
- Word文档 (`.docx`)
- PDF文档 (`.pdf`) - 可搜索
- Excel表格 (`.xlsx`)

### 模式说明

#### 通用模式
- 适用于常规文档、文章、报告等
- 识别所有文本内容和基础版面结构
- 支持多种输出格式

#### 表格模式
- 专注于表格数据识别
- 支持复杂表格结构
- 可将多页表格合并到同一Excel文件

## 配置说明

### 应用设置
应用程序的配置文件保存在用户目录下的`.prisma_ocr/config.toml`文件中。

### 模型文件
首次运行时，应用程序会自动下载所需的模型文件到`resources/models/`目录。

## 测试说明

### 📋 最新测试报告
查看完整的测试结果和项目状态: [最终测试报告](tests/FINAL_TEST_REPORT.md)

### 测试文件结构
```
tests/
├── README.md              # 测试说明文档
├── run_all_tests.py       # 运行所有测试
├── test_summary.py        # 环境检查总结
├── test_dependencies.py   # 依赖包检查
├── test_simple.py         # 简单模块导入测试
├── test_basic.py          # 基础功能测试
├── test_config.py         # 配置系统测试
├── test_ocr_pdf.py        # PDF OCR功能测试
├── test_pdf_processing.py # PDF处理专项测试
└── process_pdf.py         # 完整PDF处理脚本
```

### 运行测试

#### 快速环境检查
```bash
python tests/test_summary.py
```

#### 依赖检查
```bash
python tests/test_dependencies.py
```

#### 运行所有测试
```bash
python tests/run_all_tests.py
```

#### 测试PDF处理功能
```bash
# 使用项目根目录下的ocr测试.pdf
python tests/test_pdf_processing.py
python tests/process_pdf.py
```

## 开发说明

### 项目结构
```
Prisma-OCR/
├── main.py                 # 应用程序入口
├── run.py                  # 启动脚本
├── install.py              # 安装脚本
├── requirements.txt        # 依赖列表
├── tests/                  # 测试文件夹
│   ├── test_*.py          # 各种测试脚本
│   └── run_all_tests.py   # 测试套件
├── config/                 # 配置模块
│   ├── settings.py        # 应用设置
│   └── models_config.py   # 模型配置
├── core/                   # 核心功能模块
│   └── ocr_engine.py      # OCR引擎（支持PP-DocTranslation）
├── webui/                  # Web界面模块
│   ├── web_controller.py  # Web控制器
│   ├── api_bridge.py      # API桥接
│   ├── ocr_api.py         # OCR API
│   └── file_api.py        # 文件API
├── utils/                  # 工具模块
│   ├── file_utils.py      # 文件工具
│   ├── image_utils.py     # 图像工具
│   └── system_monitor.py  # 系统监控
└── resources/              # 资源文件
    ├── icons/             # 图标
    ├── styles/            # 样式
    └── models/            # 模型文件
```

### 开发环境设置
1. 运行安装脚本: `python install.py`
2. 或手动安装依赖: `pip install -r requirements.txt`
3. 运行环境检查: `python tests/test_summary.py`
4. 运行所有测试: `python tests/run_all_tests.py`

## 常见问题

### Q: 首次启动很慢？
A: 首次启动需要下载模型文件，请耐心等待。后续启动会很快。

### Q: 处理大文件时内存不足？
A: 可以在高级选项中降低PDF分辨率，或者分批处理大文件。

### Q: GPU加速不工作？
A: 确保安装了CUDA和对应版本的paddlepaddle-gpu。

### Q: 翻译功能不可用？
A: 翻译功能需要下载较大的模型文件，请确保网络连接正常。

## 技术支持

如遇到问题，请提供以下信息：
- 操作系统版本
- Python版本
- 错误日志（位于`logs/`目录）
- 问题复现步骤

## 许可证

本项目基于MIT许可证开源。

## 致谢

- [PaddleOCR](https://github.com/PaddlePaddle/PaddleOCR) - 核心OCR引擎
- [PyQt6](https://www.riverbankcomputing.com/software/pyqt/) - GUI框架
- 其他开源项目和贡献者

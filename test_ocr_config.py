#!/usr/bin/env python3
"""
测试OCR配置和模式设置
验证重构后的配置是否正确
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import AppSettings
from config.models_config import ModelsConfig
from core.ocr_engine import OCREngine

def test_settings_configuration():
    """测试设置配置"""
    print("=" * 60)
    print("测试设置配置")
    print("=" * 60)
    
    settings = AppSettings()
    
    print("OCR设置:")
    print(f"  - 使用DocTranslation: {settings.ocr.use_doc_translation}")
    print(f"  - 检测模型: {settings.ocr.text_detection_model_name}")
    print(f"  - 识别模型: {settings.ocr.text_recognition_model_name}")
    print(f"  - 使用GPU: {settings.ocr.use_gpu}")
    print(f"  - 语言: {settings.ocr.lang}")
    
    print("\nDocTranslation配置:")
    if settings.ocr.doc_translation_config:
        for key, value in settings.ocr.doc_translation_config.items():
            print(f"  - {key}: {value}")
    
    return settings

def test_models_configuration():
    """测试模型配置"""
    print("\n" + "=" * 60)
    print("测试模型配置")
    print("=" * 60)
    
    models_config = ModelsConfig()
    
    print("OCR模型:")
    for key, model in models_config.ocr_models.items():
        print(f"  - {key}: {model.name}")
        print(f"    URL: {model.url}")
        print(f"    大小: {model.size_mb}MB")
    
    return models_config

def test_ocr_engine_modes():
    """测试OCR引擎模式配置"""
    print("\n" + "=" * 60)
    print("测试OCR引擎模式配置")
    print("=" * 60)
    
    settings = AppSettings()
    models_config = ModelsConfig()
    
    ocr_engine = OCREngine(settings.ocr, models_config)
    
    print("支持的模式:")
    for mode, config in ocr_engine.mode_configs.items():
        print(f"\n{mode}模式:")
        print(f"  - 表格识别: {config['use_table_recognition']}")
        print(f"  - 印章识别: {config['use_seal_recognition']}")
        print(f"  - 公式识别: {config['use_formula_recognition']}")
        print(f"  - 区域检测: {config['use_region_detection']}")
        print(f"  - 检测模型: {config['text_detection_model_name']}")
        print(f"  - 识别模型: {config['text_recognition_model_name']}")
    
    return ocr_engine

def test_mode_switching():
    """测试模式切换"""
    print("\n" + "=" * 60)
    print("测试模式切换")
    print("=" * 60)
    
    settings = AppSettings()
    models_config = ModelsConfig()
    ocr_engine = OCREngine(settings.ocr, models_config)
    
    modes_to_test = ['general', 'table']
    
    for mode in modes_to_test:
        try:
            ocr_engine.set_mode(mode)
            print(f"✅ 成功切换到{mode}模式")
            print(f"   当前模式: {ocr_engine.current_mode}")
            
            # 获取当前模式的配置
            current_config = ocr_engine.mode_configs[mode]
            print(f"   表格识别: {current_config['use_table_recognition']}")
            
        except Exception as e:
            print(f"❌ 切换到{mode}模式失败: {e}")
    
    # 测试无效模式
    try:
        ocr_engine.set_mode('invalid_mode')
        print("❌ 应该抛出异常")
    except ValueError as e:
        print(f"✅ 正确处理无效模式: {e}")

def test_configuration_differences():
    """测试general和table模式的配置差异"""
    print("\n" + "=" * 60)
    print("测试general和table模式的配置差异")
    print("=" * 60)
    
    settings = AppSettings()
    models_config = ModelsConfig()
    ocr_engine = OCREngine(settings.ocr, models_config)
    
    general_config = ocr_engine.mode_configs['general']
    table_config = ocr_engine.mode_configs['table']
    
    print("配置对比:")
    print(f"{'配置项':<30} {'General':<10} {'Table':<10}")
    print("-" * 50)
    
    config_keys = [
        'use_table_recognition',
        'use_seal_recognition', 
        'use_formula_recognition',
        'use_region_detection',
        'text_detection_model_name',
        'text_recognition_model_name'
    ]
    
    for key in config_keys:
        general_val = general_config.get(key, 'N/A')
        table_val = table_config.get(key, 'N/A')
        
        # 截断长字符串
        if isinstance(general_val, str) and len(str(general_val)) > 8:
            general_val = str(general_val)[:8] + "..."
        if isinstance(table_val, str) and len(str(table_val)) > 8:
            table_val = str(table_val)[:8] + "..."
            
        print(f"{key:<30} {str(general_val):<10} {str(table_val):<10}")
    
    # 重点检查差异
    print(f"\n关键差异:")
    print(f"  - General模式表格识别: {general_config['use_table_recognition']}")
    print(f"  - Table模式表格识别: {table_config['use_table_recognition']}")

def main():
    """主测试函数"""
    print("OCR配置测试")
    print("=" * 60)
    
    try:
        # 测试设置配置
        settings = test_settings_configuration()
        
        # 测试模型配置
        models_config = test_models_configuration()
        
        # 测试OCR引擎模式
        ocr_engine = test_ocr_engine_modes()
        
        # 测试模式切换
        test_mode_switching()
        
        # 测试配置差异
        test_configuration_differences()
        
        print("\n" + "=" * 60)
        print("✅ 所有配置测试通过")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
